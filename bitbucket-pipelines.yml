definitions:
  services:
    docker:
      memory: 2048

pipelines:
  custom:
    deployment-to-prod:
      - step:
          name: Build
          services: [docker]
          runs-on:  [self.hosted]
          clone:
            depth: 1
          script:
            - make
            - make push
      - step:
          name: Deploy to PRODUCTION
          deployment: production
          image: janniet/build-pipe
          runs-on:  [self.hosted]
          clone:
            depth: 1
          script:
            - make deploy-production
  branches:
    develop:
      - step:
          name: Build
          services: [docker]
          runs-on:  [self.hosted]
          clone:
            depth: 1
          script:
            - make
            - make push
      - step:
          name: Deploy to TEST
          deployment: test
          image: janniet/build-pipe
          runs-on:  [self.hosted]
          clone:
            depth: 1
          script:
            - make deploy
    master:
      - step:
          name: Build
          services: [docker]
          runs-on:  [self.hosted]
          clone:
            depth: 1
          script:
            - make
            - make push
      - step:
          name: Deploy to Staging
          deployment: staging
          image: janniet/build-pipe
          runs-on:  [self.hosted]
          clone:
            depth: 1
          script:
            - make deploy

