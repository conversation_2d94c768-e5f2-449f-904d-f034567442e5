SERVER=***************
#SERVER=*************

APP_NAME="YR PBX"
APP_ENV=staging
APP_KEY=base64:cPqq9N2+3qVe0ftmnTMzy4ep6yF9pM20rZmDpgXHmRQ=
APP_DEBUG=true
APP_URL=https://2022.y-r.by
APP_DOMAIN=2022.y-r.by
YR_BACKEND_URL="https://2022.y-r.by"

#APP_URL=https://yves-rocher-backend-staging.228.by

LOG_CHANNEL=stack
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=debug

DB_CONNECTION=mysql
DB_HOST=***************
DB_PORT=3306
DB_DATABASE=yr_pbx_staging
DB_USERNAME=yr
DB_PASSWORD=yr

#DB_CONNECTION=mysql
#DB_HOST=************
#DB_PORT=3306
#DB_DATABASE=yr_pbx_staging
#DB_USERNAME=root
#DB_PASSWORD=

BROADCAST_DRIVER=pusher
CACHE_DRIVER=file
FILESYSTEM_DISK=local
QUEUE_CONNECTION=sync
SESSION_DRIVER=file
SESSION_LIFETIME=120

MEMCACHED_HOST=127.0.0.1

REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379

MAIL_MAILER=smtp
MAIL_HOST=mailpit
MAIL_PORT=1025
MAIL_USERNAME=null
MAIL_PASSWORD=null
MAIL_ENCRYPTION=null
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="${APP_NAME}"

AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_DEFAULT_REGION=us-east-1
AWS_BUCKET=
AWS_USE_PATH_STYLE_ENDPOINT=false

PUSHER_APP_ID=1
PUSHER_APP_KEY=8NS6T
PUSHER_APP_SECRET=bQ53fHvL69BWHkVr4uFWSUQs
PUSHER_HOST=pbxpusher
PUSHER_PORT=6001
PUSHER_SCHEME=http
PUSHER_APP_CLUSTER=mt1

VITE_PUSHER_APP_KEY="${PUSHER_APP_KEY}"
VITE_PUSHER_HOST="${PUSHER_HOST}"
VITE_PUSHER_PORT="${PUSHER_PORT}"
VITE_PUSHER_SCHEME="${PUSHER_SCHEME}"
VITE_PUSHER_APP_CLUSTER="${PUSHER_APP_CLUSTER}"

PBX_API_URL=http://************:8083/click2call.php
PBX_AUTH_TOKEN=8gguop90l74zbv5iust6ppcw1xwr2ibk1
