<?php

use App\Http\Controllers\PBX\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|
*/

Route::get('webhook/calls', [Controller::class, 'calls']);
Route::post('webhook/start-call', [Controller::class, 'startCall']);
Route::post('webhook/operator-dial-start', [Controller::class, 'operatorDialStart']);
Route::post('webhook/operator-dial-finish', [Controller::class, 'operatorDialFinish']);
Route::post('webhook/operator-answered', [Controller::class, 'operatorAnswered']);
Route::post('webhook/finish-call', [Controller::class, 'finishCall']);
