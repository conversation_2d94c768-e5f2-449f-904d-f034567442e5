<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Log;

class LogRequests
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure(\Illuminate\Http\Request): (\Illuminate\Http\Response|\Illuminate\Http\RedirectResponse)  $next
     * @return Response|\Illuminate\Http\RedirectResponse
     */
    public function handle(Request $request, Closure $next, $logChannel = null)
    {
        /* @var Response $response */
        $response = $next($request);

        $tag = sprintf('%s %s %s', $request->method(), $response->getStatusCode(), $request->url());

        Log::channel($logChannel)->info($tag, [
            'request' => $request->getContent(),
            'response' => $response->getContent(),
        ]);

        return $response;
    }
}
