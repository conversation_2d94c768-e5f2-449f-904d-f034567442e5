<?php

namespace App\Http\Controllers\PBX;

use App\Enums\CallStates;
use App\Http\Requests\PBX\FinishCallRequest;
use App\Http\Requests\PBX\OperatorAnsweredRequest;
use App\Http\Requests\PBX\OperatorDialFinishRequest;
use App\Http\Requests\PBX\OperatorDialStartRequest;
use App\Http\Requests\PBX\StartCallRequest;
use App\Models\Call;
use Illuminate\Http\Request;

class Controller extends \App\Http\Controllers\Controller
{
    public function calls(Request $request)
    {
        return Call::latest()->paginate();
    }

    public function startCall(StartCallRequest $request)
    {
        $call = Call::updateOrCreate([
            'external_id' => $request->input('call_id'),
        ], [
            ...$request->validated(),
            'state' => CallStates::Initiated,
        ]);
    }

    public function operatorDialStart(OperatorDialStartRequest $request)
    {
        $call = Call::updateOrCreate([
            'external_id' => $request->input('call_id'),
        ], [
            'operator_extension' => $request->input('operator_exten'),
            'state' => CallStates::Ringing,
        ]);
    }

    public function operatorAnswered(OperatorAnsweredRequest $request)
    {
        $call = Call::updateOrCreate([
            'external_id' => $request->input('call_id'),
        ], [
            'operator_extension' => $request->input('operator_exten'),
            'state' => CallStates::InProgress,
        ]);
    }

    public function operatorDialFinish(OperatorDialFinishRequest $request)
    {
        $call = Call::updateOrCreate([
            'external_id' => $request->input('call_id'),
        ], [
            'operator_extension' => $request->input('operator_exten'),
            'state' => CallStates::Finished,
        ]);
    }

    public function finishCall(FinishCallRequest $request)
    {
        $call = Call::updateOrCreate([
            'external_id' => $request->input('call_id'),
        ], [
            'duration' => $request->input('duration'),
            'dial_status' => $request->input('dialstatus'),
            'direction' => $request->input('direction'),
            'call_start_date' => $request->input('call_start_date'),
            'record_url' => $request->input('record_url'),
            'state' => CallStates::Finished,
        ]);
    }
}
