<?php

namespace App\Http\Controllers\API;

use App\Enums\CallStates;
use App\Http\Controllers\Controller;
use App\Http\Requests\API\CallCreateRequest;
use App\Http\Requests\API\CallIndexRequest;
use App\Http\Resources\API\CallResource;
use App\Models\Call;
use App\Services\PBXService;
use Illuminate\Http\Request;

class CallController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Resources\Json\AnonymousResourceCollection
     */
    public function index(CallIndexRequest $request)
    {
        $query = Call::query();

        if ($request->has('filter.state')) {
            $query->where('state', $request->input('filter.state'));
        }

        if ($request->has('filter.operator_extension')) {
            $query->where('operator_extension', $request->input('filter.operator_extension'));
        }

        if ($request->has('filter.client_number')) {
            $query->where('client_number', $request->input('filter.client_number'));
        }

        if ($request->has('filter.direction')) {
            $query->where('direction', $request->input('filter.direction'));
        }

        if ($request->has('filter.call_start_date.min')) {
            $query->where('call_start_date', '>=', $request->input('filter.call_start_date.min'));
        }

        if ($request->has('filter.call_start_date.max')) {
            $query->where('call_start_date', '<=', $request->input('filter.call_start_date.max'));
        }

        return CallResource::collection(
            $query->latest()->paginate()
        );
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\Response
     * @throws \Illuminate\Http\Client\RequestException
     */
    public function store(CallCreateRequest $request, PBXService $service)
    {
        return $service->initiateCall(
            $request->operator_extension,
            $request->phone_number,
        );
    }

    public function activeCall(Request $request)
    {
        $query = Call::query()
            ->whereNot('state', CallStates::Finished)
            ->orderByDesc('id');

        if ($request->has('operator_extension')) {
            $query->where('operator_extension', $request->input('operator_extension'));
        }

        if ($request->has('client_number')) {
            $msisdn = preg_replace('/\D/', '', $request->input('client_number'));
            $query->where('client_number', $msisdn);
        }

        if ($call = $query->first()) {
            return new CallResource($call);
        }

        return null;

    }
}
