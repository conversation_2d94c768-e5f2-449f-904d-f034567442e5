<?php

namespace App\Http\Requests\PBX;

use Illuminate\Foundation\Http\FormRequest;

class OperatorAnsweredRequest extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            'call_id' => ['required', 'exists:calls,external_id'],
            'operator_exten' => ['required'],
        ];
    }
}
