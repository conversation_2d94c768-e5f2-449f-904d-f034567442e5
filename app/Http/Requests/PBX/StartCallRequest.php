<?php

namespace App\Http\Requests\PBX;

use App\Enums\CallDirections;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use Illuminate\Validation\Rules\Enum;

class StartCallRequest extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            'call_id' => ['required'],
            'client_id' => ['nullable'],
            'client_name' => ['nullable', 'string'],
            'client_number' => ['required'],
            'direction' => ['required', new Enum(CallDirections::class)],
            'call_start_date' => ['required', 'date'],
        ];
    }

    protected function prepareForValidation()
    {
        if ($this->has('client_number')) {

            $msisdn = preg_replace('/\D/', '', $this->input('client_number'));

            if (Str::startsWith($msisdn, '80')) {
                $msisdn = sprintf('375%s', substr($msisdn, 2));
            }

            try {
                $clientInformation = $this->getClientInformation($msisdn);

                $this->merge([
                    'client_id' => $clientInformation['id'] ?? null,
                    'client_name' => $clientInformation['name'] ?? null,
                    'client_number' => $msisdn,
                ]);
            } catch (\Exception $e) {
                Log::error($e);
            }
        }
    }

    private function getClientInformation($msisdn)
    {
        $apiUrl = env('YR_BACKEND_URL', 'https://api.y-r.by');

        return Http::get("{$apiUrl}/m2m/asterisk/users/find", [
            'msisdn' => $msisdn,
        ])->json();
    }
}
