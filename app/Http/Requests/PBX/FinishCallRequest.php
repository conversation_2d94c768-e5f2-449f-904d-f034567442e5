<?php

namespace App\Http\Requests\PBX;

use App\Enums\CallDialStatuses;
use App\Enums\CallDirections;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rules\Enum;

class FinishCallRequest extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            'call_id' => ['required', 'exists:calls,external_id'],
            'duration' => ['integer'],
            'dialstatus' => [new Enum(CallDialStatuses::class)],
            'direction' => ['required', new Enum(CallDirections::class)],
            'call_start_date' => ['date'],
            'record_url' => [],
        ];
    }
}
