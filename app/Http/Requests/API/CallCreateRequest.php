<?php

namespace App\Http\Requests\API;

use Illuminate\Foundation\Http\FormRequest;

class CallCreateRequest extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            'operator_extension' => ['required'],
            'phone_number' => ['required'],
        ];
    }
}
