<?php

namespace App\Http\Requests\API;

use App\Enums\CallStates;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rules\Enum;

class CallIndexRequest extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            'filter.state' => [new Enum(CallStates::class)],
            'filter.operator_extension' => [],
            'filter.client_number' => [],
        ];
    }
}
