<?php

namespace App\Providers;

use App\Services\PBXService;
use Illuminate\Support\ServiceProvider;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     *
     * @return void
     */
    public function register()
    {
        //
    }

    /**
     * Bootstrap any application services.
     *
     * @return void
     */
    public function boot()
    {
        $this->app->singleton(PBXService::class, function () {
            return new PBXService(config('pbx'));
        });
    }
}
