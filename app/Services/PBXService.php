<?php

namespace App\Services;

use Illuminate\Http\Client\RequestException;
use Illuminate\Support\Facades\Http;

class PBXService
{
    private array $config;

    public function __construct($config)
    {
        $this->config = $config;
    }

    /**
     * @throws RequestException
     */
    public function initiateCall($operatorExtension, $phoneNumber)
    {
        return Http::asForm()->post($this->config['api_url'], [
            'auth_token' => $this->config['auth_token'],
            'operator_exten' => $operatorExtension,
            'phone_number' => $phoneNumber,
        ])->throw()->json();
    }
}