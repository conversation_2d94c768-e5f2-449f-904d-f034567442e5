<?php

namespace App\Models;

use App\Enums\CallDialStatuses;
use App\Enums\CallDirections;
use App\Enums\CallStates;
use App\Events\CallUpdated;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Call extends Model
{
    use HasFactory;
    use HasUuids;

    protected $fillable = [
        'external_id',
        'client_id',
        'client_name',
        'client_number',
        'operator_extension',
        'direction',
        'dial_status',
        'state',
        'call_start_date',
        'duration',
        'record_url',
    ];

    protected $casts = [
        'direction' => CallDirections::class,
        'state' => CallStates::class,
        'call_start_date' => 'datetime',
    ];

    protected $attributes = [
        'dial_status' => CallDialStatuses::NoAnswer,
    ];

    protected static function boot()
    {
        parent::boot(); // TODO: Change the autogenerated stub

        static::saved(function ($call) {
            CallUpdated::dispatch($call);
        });
    }
}
