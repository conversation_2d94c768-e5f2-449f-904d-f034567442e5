version: "3.4"

x-app: &app
  image: ${APP_IMAGE}
  restart: unless-stopped
  environment:
    APP_ENV: test
  networks:
    - pbx
    - traefik
  extra_hosts:
    - "host.docker.internal:host-gateway"

services:
  pbx-app:
    <<: *app
    labels:
      - "traefik.enable=true"
      - "traefik.docker.network=traefik"
      - "traefik.http.routers.${REPO_BRANCH_SLUG}.rule=Host(`${APP_DOMAIN}`) && PathPrefix(`/pbx/webhook`)"


  pbxpusher:
    <<: *app
    command: php artisan websockets:serve --debug
    labels:
      - "traefik.enable=true"
      - "traefik.docker.network=traefik"
      - "traefik.http.routers.${REPO_BRANCH_SLUG}-pusher.rule=Host(`${APP_DOMAIN}`) && PathPrefix(`/app/${PUSHER_APP_KEY}`)"
      - "traefik.http.services.${REPO_BRANCH_SLUG}-pusher.loadbalancer.server.port=6001"

networks:
  traefik:
    external: true
  pbx:
    external: true
