ifneq (,$(wildcard ./.env.$(BITBUCKET_DEPLOYMENT_ENVIRONMENT)))
    include .env.$(BITBUCKET_DEPLOYMENT_ENVIRONMENT)
endif

export

PROJECT_FOLDER_NAME = $(shell basename $(abspath $(dir $$PWD)))
BITBUCKET_REPO_SLUG ?= $(PROJECT_FOLDER_NAME)
BITBUCKET_BRANCH ?= master
DOCKER_REGISTRY ?= registry.228.by
APP_IMAGE=$(DOCKER_REGISTRY)/$(BITBUCKET_REPO_SLUG):$(BITBUCKET_BRANCH)

all: build

build:
	@docker build -t $(APP_IMAGE) .

push:
	@docker push $(APP_IMAGE)

deploy:
	$(MAKE) --directory=deployment deploy

deploy-production:
	$(MAKE) --directory=deployment deploy-production
