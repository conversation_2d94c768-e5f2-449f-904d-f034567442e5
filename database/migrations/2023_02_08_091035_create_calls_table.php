<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('calls', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->string('external_id')->index();
            $table->string('client_id')->nullable()->index();
            $table->string('client_name')->nullable();
            $table->string('client_number')->nullable()->index();
            $table->string('operator_extension')->nullable()->index();
            $table->enum('direction', array_column(\App\Enums\CallDirections::cases(), 'value'))->index();
            $table->enum('dial_status', array_column(\App\Enums\CallDialStatuses::cases(), 'value'))->nullable();
            $table->dateTime('call_start_date')->nullable()->index();
            $table->unsignedInteger('duration')->nullable();
            $table->string('record_url')->nullable();
            $table->enum('state', array_column(\App\Enums\CallStates::cases(), 'value'))->index();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('calls');
    }
};
